(()=>{"use strict";const e=window.React,s=window.ReactDOM,{ajaxurl:i,nonce:r,plugin_root_url:t,data:a,count:n,last_updated:l,domain:c,original_domain:o}=window.trustpilot_reviewkit,d=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),m=e=>{const s=`stars-${(Math.round(2*e)/2).toFixed(1).replace(".","_")}.svg`;return t+"/assets/images/"+s},u=e=>e>=4.5?"Excellent":e>=4?"Great":e>=3?"Good":e>=2?"Average":"Poor",v=window.ReactJSXRuntime,h=(0,e.createContext)();function p({children:s}){const[i,r]=(0,e.useState)("shortcodes"),[t,n]=(0,e.useState)(a||{}),l={tab:i,setTab:r,data:t,setData:n};return(0,v.jsx)(h.Provider,{value:l,children:s})}function w(){return(0,e.useContext)(h)}const x=()=>{const{tab:e,setTab:s}=w();return(0,v.jsx)("nav",{children:(0,v.jsx)("ul",{children:_.map((i=>(0,v.jsx)("li",{className:e===i.tab?"active":"",onClick:()=>s(i.tab),children:i.label},i.label)))})})},_=[{label:"Reviews",tab:"reviews"},{label:"Shortcodes",tab:"shortcodes"}],j=window.wp.components,g=window.wp.i18n,N=({type:e="",...s})=>"light"===e?(0,v.jsx)("img",{src:t+"/assets/images/light-single-star.png",alt:"Trustpilot Logo",style:{width:"20px"}}):(0,v.jsx)("img",{src:t+"/assets/images/single-star.svg",alt:"Trustpilot Logo",...s}),f=({trustscore:e,reviews:s})=>{const i=a?.business_details||{},r=e||i.trustScore||4.8,t=s||i.numberOfReviews||347;return(0,v.jsxs)("div",{className:"reviewkit_fpln_mini preview",children:[(0,v.jsxs)("div",{className:"reviewkit_fpln_inner_top",children:[(0,v.jsx)(N,{}),(0,v.jsx)("span",{className:"place_name",children:(0,g.__)("Trustpilot","reviewkit")})]}),(0,v.jsx)("div",{className:"reviewkit_bg",children:(0,v.jsx)("img",{src:m(r),alt:`${r} star rating`})}),(0,v.jsxs)("div",{className:"reviewkit_fpln_inner_bottom",children:[(0,v.jsxs)("div",{className:"reviewkit_left_reviews",children:[(0,v.jsxs)("span",{className:"review_us_one",children:[(0,g.__)("TrustScore","reviewkit")," "]}),(0,v.jsx)("span",{className:"reviewkit_orignal_rcount",children:r})]}),(0,v.jsxs)("div",{className:"reviewkit_review_area",children:[(0,v.jsx)("span",{className:"reviewkit_out_of",children:d(t)}),(0,v.jsxs)("span",{className:"reviewkit_reviews",children:[" ",(0,g.__)("reviews","reviewkit")]})]})]})]})},b=({reviews:e})=>{const s=a?.business_details||{},i=e||s.numberOfReviews||1376,r=s.trustScore||4.8;return(0,v.jsxs)("div",{className:"reviewkit_fpln_starter preview",children:[(0,v.jsxs)("div",{className:"reviewkit_fpln_inner_top",children:[(0,v.jsxs)("span",{className:"mirc_see",children:[(0,g.__)("Check out our","reviewkit")," "]}),(0,v.jsx)("span",{className:"mirc_r_count",children:d(i)}),(0,v.jsxs)("span",{className:"review_us_one",children:[" ",(0,g.__)("reviews","reviewkit")]})]}),(0,v.jsx)("div",{className:"reviewkit_bg",children:(0,v.jsx)("img",{src:m(r),alt:`${r} star rating`})}),(0,v.jsx)("div",{className:"reviewkit_toltip_wrap",children:(0,v.jsxs)("div",{className:"reviewkit_logo_container",children:[(0,v.jsx)(y,{}),(0,v.jsx)("img",{src:t+"/assets/images/single-star.svg",alt:"Trustpilot Logo"}),(0,v.jsx)("span",{className:"reviewkit_text",children:"Trustpilot"})]})})]})},y=()=>(0,v.jsxs)("div",{className:"reviewkit_tooltip reviewkit_tooltip_large",children:[(0,v.jsx)("div",{className:"reviewkit_tooltip_content",children:(0,g.__)("Helping each other make better choices","reviewkit")}),(0,v.jsx)("a",{href:"#",className:"reviewkit_tooltip_link",children:(0,g.__)("Read and write reviews","reviewkit")})]}),k=({rating:e,score:s})=>{const i=s||(a?.business_details||{}).trustScore||4.8,r=e||(e=>e>=4.5?"Excellent":e>=4?"Great":e>=3?"Good":e>=2?"Average":"Poor")(i);return(0,v.jsxs)("div",{className:"reviewkit_fpln_mcts reviewkit_fpln_common preview",children:[(0,v.jsxs)("div",{className:"reviewkit_fpln_inner_left",children:[(0,v.jsx)("span",{className:"review_us_one",children:r}),(0,v.jsxs)("div",{className:"reviewkit_score",children:[(0,v.jsx)("span",{className:"reviewkit_orignal_rcount",children:i}),(0,v.jsx)("span",{className:"reviewkit_out_of",children:" out of 5"})]})]}),(0,v.jsxs)("div",{className:"reviewkit_fpln_inner_right",children:[(0,v.jsx)(N,{}),(0,v.jsx)("span",{className:"place_name",children:"Trustpilot"})]})]})},C=({rating:e,stars:s})=>{const i=(a?.business_details||{}).trustScore||4.8,r=e||u(i);return(0,v.jsxs)("div",{className:"reviewkit_fpln_mcs reviewkit_fpln_common preview",children:[(0,v.jsx)("div",{className:"dynamic_rating",children:r}),(0,v.jsx)("div",{className:"dynamic_stars",children:(0,v.jsx)("img",{src:m(i),alt:`${r} star rating`,style:{width:"100%"}})}),(0,v.jsxs)("div",{className:"tp-wrapper",children:[(0,v.jsx)(N,{}),(0,v.jsx)("div",{className:"place_name",children:(0,g.__)("Trustpilot","reviewkit")})]})]})},S=({count:e})=>{const s=e||(a?.business_details||{}).numberOfReviews||437;return(0,v.jsx)("div",{children:(0,v.jsxs)("a",{className:"reviewkit_fpln_mirc reviewkit_fpln_common preview",href:"#",children:[(0,v.jsx)("span",{className:"mirc_see",children:(0,g.__)("See our","reviewkit")}),(0,v.jsx)("span",{className:"mirc_r_count",children:d(s)}),(0,v.jsx)("span",{className:"review_us_one",children:(0,g.__)("reviews on","reviewkit")}),(0,v.jsx)(N,{style:{width:"20px"}}),(0,v.jsx)("span",{className:"place_name",children:(0,g.__)("Trustpilot","reviewkit")})]})})},T=({reviews:e})=>{const s=e||(a?.business_details||{}).numberOfReviews||1300;return(0,v.jsxs)("div",{className:"reviewkit_fpln_mcb_wrap",children:[(0,v.jsxs)("a",{className:"reviewkit_fpln_mcb_left",href:"#",children:[(0,v.jsx)(N,{type:"light"}),(0,v.jsx)("span",{className:"place_name",children:(0,g.__)("Trustpilot","reviewkit")})]}),(0,v.jsxs)("div",{className:"reviewkit_fpln_mcb_right",children:[d(s)," ",(0,g.__)("reviews","reviewkit")]})]})},R=({rating:e,stars:s,reviews:i})=>{const r=a?.business_details||{},t=r.trustScore||4.8,n=i||r.numberOfReviews||437,l=e||u(t);return(0,v.jsxs)("div",{className:"reviewkit_fpln_mc reviewkit_fpln_common preview",children:[(0,v.jsxs)("div",{className:"reviewkit_fpln_mc_inner_left",children:[(0,v.jsx)("span",{className:"review_us_one",children:l}),(0,v.jsx)("div",{className:"reviewkit_star_rating",children:(0,v.jsx)("img",{src:m(t),alt:`${l} star rating`,style:{width:"100%"}})})]}),(0,v.jsxs)("div",{className:"reviewkit_fpln_mc_inner_right",children:[(0,v.jsx)("span",{className:"mirc_r_count",children:d(n)}),(0,v.jsx)("span",{className:"review_us_one",children:(0,g.__)("reviews on","reviewkit")}),(0,v.jsx)(N,{style:{width:"18px"}}),(0,v.jsx)("span",{className:"place_name",children:(0,g.__)("Trustpilot","reviewkit")})]})]})},L=()=>{const[s,i]=(0,e.useState)(""),[r,t]=(0,e.useState)(""),a=[{id:"mini",name:"Mini Trustbox",shortcode:"[reviewkit_trustpilot_mini]",description:"Compact widget showing TrustScore, star rating, and review count",category:"free",preview:f,attributes:[{name:"trustscore",default:"4.8",description:"Trust score rating (0-5)"},{name:"reviews",default:"347",description:"Number of reviews"},{name:"url",default:"#",description:"Link URL"}]},{id:"starter",name:"Starter Trustbox",shortcode:"[reviewkit_trustpilot_starter]",description:"Interactive widget with star rating and tooltip",category:"free",preview:b,attributes:[{name:"reviews",default:"1,376",description:"Number of reviews"},{name:"url",default:"#",description:"Link URL"}]},{id:"micro_trustscore",name:"Micro TrustScore",shortcode:"[reviewkit_trustpilot_micro_trustscore]",description:"Simple display of TrustScore rating",category:"free",preview:k,attributes:[{name:"rating",default:"Excellent",description:"Rating text"},{name:"score",default:"4.8",description:"Numeric score"},{name:"url",default:"#",description:"Link URL"}]},{id:"micro_star",name:"Micro Star",shortcode:"[reviewkit_trustpilot_micro_star]",description:"Interactive star rating display",category:"free",preview:C,attributes:[{name:"rating",default:"Excellent",description:"Rating text"},{name:"stars",default:"5",description:"Number of stars (1-5)"},{name:"url",default:"#",description:"Link URL"}]},{id:"micro_reviewcount",name:"Micro Review Count",shortcode:"[reviewkit_trustpilot_micro_reviewcount]",description:"Link showing review count",category:"free",preview:S,attributes:[{name:"count",default:"437",description:"Number of reviews"},{name:"url",default:"#",description:"Link URL"}]},{id:"micro_button",name:"Micro Button",shortcode:"[reviewkit_trustpilot_micro_button]",description:"Button-style widget with review count",category:"free",preview:T,attributes:[{name:"reviews",default:"1.3K+",description:"Review count display"},{name:"url",default:"#",description:"Link URL"}]},{id:"micro_combo",name:"Micro Combo",shortcode:"[reviewkit_trustpilot_micro_combo]",description:"Combined star rating and review count widget",category:"free",preview:R,attributes:[{name:"rating",default:"Excellent",description:"Rating text"},{name:"stars",default:"5",description:"Number of stars (1-5)"},{name:"reviews",default:"437",description:"Number of reviews"},{name:"url",default:"#",description:"Link URL"}]}].filter((e=>e.name.toLowerCase().includes(s.toLowerCase())||e.description.toLowerCase().includes(s.toLowerCase())||e.shortcode.toLowerCase().includes(s.toLowerCase())));return(0,v.jsxs)("div",{className:"page shortcodes-page",children:[(0,v.jsxs)("div",{className:"shortcodes-header",children:[(0,v.jsx)("h2",{children:(0,g.__)("Trustpilot Shortcodes","trustpilot-reviewkit")}),(0,v.jsx)("p",{children:(0,g.__)("Use these shortcodes to display Trustpilot widgets on your website.","trustpilot-reviewkit")}),(0,v.jsx)(j.TextControl,{label:(0,g.__)("Search Shortcodes","trustpilot-reviewkit"),value:s,onChange:i,placeholder:(0,g.__)("Search by name, description, or shortcode...","trustpilot-reviewkit"),className:"shortcodes-search"})]}),(0,v.jsx)("div",{className:"shortcodes-grid",children:a.map((e=>(0,v.jsxs)(j.Card,{className:`shortcode-card ${e.id}`,children:[(0,v.jsxs)(j.CardHeader,{children:[(0,v.jsx)("h3",{children:e.name}),(0,v.jsx)("span",{className:"shortcode-category",children:e.category})]}),(0,v.jsxs)(j.CardBody,{children:[(0,v.jsx)("div",{className:"shortcode-preview",children:e.preview?(0,v.jsx)("div",{className:"preview-component",style:{overflow:"visible"},children:(0,v.jsx)(e.preview,{})}):(0,v.jsxs)("div",{className:"preview-placeholder",children:[(0,v.jsx)("span",{children:"📊"}),(0,v.jsx)("small",{children:(0,g.__)("Preview","trustpilot-reviewkit")})]})}),(0,v.jsx)("p",{className:"shortcode-description",children:e.description}),(0,v.jsxs)("div",{className:"shortcode-code",children:[(0,v.jsx)("code",{children:e.shortcode}),(0,v.jsx)(j.Button,{variant:"secondary",size:"small",onClick:()=>(async e=>{try{if(navigator.clipboard&&window.isSecureContext)return await navigator.clipboard.writeText(e),t(e),void setTimeout((()=>t("")),2e3);const s=document.createElement("textarea");s.value=e,s.style.position="fixed",s.style.left="-999999px",s.style.top="-999999px",document.body.appendChild(s),s.focus(),s.select();const i=document.execCommand("copy");if(document.body.removeChild(s),!i)throw new Error("execCommand failed");t(e),setTimeout((()=>t("")),2e3)}catch(s){console.error("Failed to copy shortcode: ",s),alert(`Copy this shortcode manually: ${e}`)}})(e.shortcode),className:r===e.shortcode?"copied":"",children:r===e.shortcode?(0,g.__)("Copied!","trustpilot-reviewkit"):(0,g.__)("Copy","trustpilot-reviewkit")})]})]})]},e.id)))}),0===a.length&&(0,v.jsx)("div",{className:"no-results",children:(0,v.jsx)("p",{children:(0,g.__)("No shortcodes found matching your search.","trustpilot-reviewkit")})})]})},E=({rating:e=0,maxStars:s=5,size:i="medium",showRating:r=!1,useSvg:t=!1})=>{const a=[],n=Math.floor(e),l=e%1!=0,c=(e=>e<=1?"#ff3722":e<=2?"#ff8622":e<=3?"#ffce00":e<=4?"#73cf11":"#00b67a")(e),o=({filled:e,color:s})=>(0,v.jsx)("svg",{className:"star-svg "+(e?"star-filled":"star-empty"),viewBox:"0 0 24 24",style:{fill:e?s:"#ddd"},children:(0,v.jsx)("path",{d:"M12 2l2.938 7.968h8.382l-6.76 4.898 2.553 7.834L12 17.334l-7.113 5.366 2.553-7.834-6.76-4.898h8.382z"})});for(let e=0;e<n;e++)a.push(t?(0,v.jsx)(o,{filled:!0,color:c},`full-${e}`):(0,v.jsx)("span",{className:"star star-full",style:{color:c},children:"★"},`full-${e}`));l&&n<s&&a.push(t?(0,v.jsxs)("div",{className:"star-svg-half",style:{position:"relative"},children:[(0,v.jsx)(o,{filled:!1,color:"#ddd"}),(0,v.jsx)("div",{style:{position:"absolute",top:0,left:0,width:"50%",overflow:"hidden"},children:(0,v.jsx)(o,{filled:!0,color:c})})]},"half"):(0,v.jsx)("span",{className:"star star-half",style:{color:c},children:"★"},"half"));const d=s-n-(l?1:0);for(let e=0;e<d;e++)a.push(t?(0,v.jsx)(o,{filled:!1,color:"#ddd"},`empty-${e}`):(0,v.jsx)("span",{className:"star star-empty",children:"★"},`empty-${e}`));return(0,v.jsxs)("div",{className:`trustpilot-star-rating trustpilot-star-rating--${i}`,children:[(0,v.jsx)("div",{className:"stars",children:a}),r&&(0,v.jsxs)("span",{className:"rating-value",children:["(",e,")"]})]})},D=({businessDetails:e={}})=>{const{displayName:s="",identifyingName:i="",numberOfReviews:r=0,trustScore:t=0,websiteUrl:a="",websiteTitle:n="",profileImageUrl:l="",stars:c=0,categories:o=[],activity:d={}}=e,m=o.find((e=>e.isPrimary))?.name||"",u=d?.verification?.verifiedUserIdentity||!1,h=d?.isClaimed||!1;return s?(0,v.jsxs)("div",{className:"trustpilot-business-page",children:[(0,v.jsxs)("div",{className:"business-header",children:[(0,v.jsxs)("div",{className:"business-logo",children:[l?(0,v.jsx)("img",{src:(w=l,w?w.startsWith("//")?`https:${w}`:w:""),alt:`${s} logo`,className:"business-image",onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="flex"}}):null,(0,v.jsx)("div",{className:"business-initials",style:{display:l?"none":"flex"},children:s.charAt(0).toUpperCase()})]}),(0,v.jsxs)("div",{className:"business-info",children:[(0,v.jsxs)("div",{className:"business-name-section",children:[(0,v.jsx)("h2",{className:"business-name",children:s}),h&&(0,v.jsx)("span",{className:"business-claimed",children:"✓ Claimed"}),u&&(0,v.jsx)("span",{className:"business-verified",children:"✓ Verified"})]}),i&&(0,v.jsx)("p",{className:"business-website",children:i}),m&&(0,v.jsx)("p",{className:"business-category",children:m}),(0,v.jsxs)("div",{className:"business-rating",children:[(0,v.jsx)(E,{rating:t,size:"large",showRating:!0,useSvg:!0}),(0,v.jsxs)("span",{className:"review-count",children:["Based on ",(p=r,p>=1e6?(p/1e6).toFixed(1)+"M":p>=1e3?(p/1e3).toFixed(1)+"K":p.toString())," reviews"]})]}),a&&(0,v.jsx)("div",{className:"business-links",children:(0,v.jsx)("a",{href:a,target:"_blank",rel:"noopener noreferrer",className:"business-website-link",children:"Visit Website"})})]})]}),d?.replyBehavior&&(0,v.jsxs)("div",{className:"business-stats",children:[(0,v.jsxs)("div",{className:"stat-item",children:[(0,v.jsx)("span",{className:"stat-label",children:"Reply Rate:"}),(0,v.jsxs)("span",{className:"stat-value",children:[d.replyBehavior.replyPercentage,"%"]})]}),d.replyBehavior.averageDaysToReply&&(0,v.jsxs)("div",{className:"stat-item",children:[(0,v.jsx)("span",{className:"stat-label",children:"Avg. Reply Time:"}),(0,v.jsxs)("span",{className:"stat-value",children:[Math.round(d.replyBehavior.averageDaysToReply)," days"]})]})]})]}):(0,v.jsx)("div",{className:"trustpilot-business-page",children:(0,v.jsx)("div",{className:"business-placeholder",children:(0,v.jsx)("p",{children:"No business information available"})})});var p,w},M=({customer:e={},size:s="medium",showImage:i=!0})=>{const{name:r="Anonymous",image:t=""}=e;return(0,v.jsxs)("div",{className:`customer-info customer-info--${s}`,children:[i&&(0,v.jsxs)("div",{className:"customer-avatar",children:[t?(0,v.jsx)("img",{src:t,alt:`${r}'s profile`,className:"customer-image",onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="flex"}}):null,(0,v.jsx)("div",{className:"customer-initials",style:{display:t?"none":"flex"},children:(e=>e&&"Customer"!==e?e.split(" ").map((e=>e.charAt(0))).join("").toUpperCase().slice(0,2):"?")(r)})]}),(0,v.jsx)("div",{className:"customer-details",children:(0,v.jsx)("span",{className:"customer-name",children:r})})]})},U=({review:e={}})=>{const{reviewId:s,rating:i=0,reviewTitle:r="",reviewBody:t="",customer:a={},dates:n={},reviewUrl:l=""}=e,{publishedDate:c,experiencedDate:o}=n,d=e=>{if(!e)return"";try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch(e){return""}};return(0,v.jsxs)("div",{className:"review-card","data-review-id":s,children:[(0,v.jsxs)("div",{className:"review-header",children:[(0,v.jsx)(M,{customer:a,size:"small"}),(0,v.jsxs)("div",{className:"review-meta",children:[(0,v.jsx)(E,{rating:i,size:"small"}),c&&(0,v.jsx)("span",{className:"review-date",children:d(c)})]})]}),(0,v.jsxs)("div",{className:"review-content",children:[r&&(0,v.jsx)("h4",{className:"review-title",children:r}),t&&(0,v.jsx)("p",{className:"review-body",children:((e,s=200)=>!e||e.length<=s?e:e.substring(0,s)+"...")(t)}),o&&(0,v.jsx)("div",{className:"review-experience-date",children:(0,v.jsxs)("small",{children:["Experience date: ",d(o)]})})]}),l&&(0,v.jsx)("div",{className:"review-footer",children:(0,v.jsx)("a",{href:l,target:"_blank",rel:"noopener noreferrer",className:"review-link",children:"View on Trustpilot"})})]})},$=({reviews:s={},initialDisplayCount:i=10,showLoadMore:r=!0})=>{const[t,a]=(0,e.useState)(i),n=Object.values(s).sort(((e,s)=>{const i=new Date(e.dates?.publishedDate||0);return new Date(s.dates?.publishedDate||0)-i})),l=n.length,c=n.slice(0,t),o=t<l,[d,m]=(0,e.useState)(null),u=d?c.filter((e=>e.rating===d)):c,h=(()=>{const e={1:0,2:0,3:0,4:0,5:0};return n.forEach((s=>{s.rating>=1&&s.rating<=5&&e[Math.floor(s.rating)]++})),e})();return 0===l?(0,v.jsx)("div",{className:"review-list",children:(0,v.jsx)("div",{className:"review-list-empty",children:(0,v.jsx)("p",{children:"No reviews available"})})}):(0,v.jsxs)("div",{className:"review-list",children:[(0,v.jsxs)("div",{className:"review-list-header",children:[(0,v.jsxs)("h3",{className:"review-list-title",children:["Customer Reviews (",l,")"]}),(0,v.jsx)("div",{className:"review-filters",children:(0,v.jsxs)("div",{className:"rating-filter",children:[(0,v.jsx)("button",{className:"filter-btn "+(null===d?"active":""),onClick:()=>m(null),children:"All"}),[5,4,3,2,1].map((e=>(0,v.jsxs)("button",{className:"filter-btn "+(d===e?"active":""),onClick:()=>m(e),children:[e,"★ (",h[e],")"]},e)))]})})]}),(0,v.jsx)("div",{className:"review-list-content",children:0===u.length?(0,v.jsx)("div",{className:"review-list-empty",children:(0,v.jsx)("p",{children:"No reviews found for the selected rating"})}):(0,v.jsx)("div",{className:"review-cards",children:u.map((e=>(0,v.jsx)(U,{review:e},e.reviewId)))})}),r&&null===d&&(0,v.jsxs)("div",{className:"review-list-controls",children:[o&&(0,v.jsxs)("button",{className:"load-more-btn",onClick:()=>{a((e=>Math.min(e+i,l)))},children:["Load More Reviews (",l-t," remaining)"]}),t>i&&(0,v.jsx)("button",{className:"show-less-btn",onClick:()=>{a(i)},children:"Show Less"})]}),(0,v.jsx)("div",{className:"review-summary",children:(0,v.jsxs)("p",{className:"review-count-summary",children:["Showing ",u.length," of ",l," reviews"]})})]})},B=()=>{const{data:s={},setData:i}=w(),{business_details:t={},reviews:a=[]}=s,[n,l]=(0,e.useState)(o),[c,d]=(0,e.useState)(!1),m=async(e=!1)=>{d(!0);const s=await((e,s={})=>{if(!e)return Promise.reject(new Error("Domain is required"));const{timeout:i=3e4}=s;return void 0===window.ajaxurl||void 0===window.trustpilot_reviewkit?Promise.reject(new Error("WordPress AJAX not available. Make sure the script is properly enqueued.")):new Promise(((t,a)=>{const n=new FormData;if(n.append("action","trustpilot_reviewkit_get_reviews"),n.append("security",r),n.append("domain",e),n.append("revalidate",s.revalidate),window.jQuery)jQuery.ajax({url:window.ajaxurl,type:"POST",data:n,processData:!1,contentType:!1,timeout:i,success:function(e){e.success?t(e.data):a(new Error(e.data?.message||"Unknown error occurred"))},error:function(e,s,i){a("timeout"===s?new Error("Request timed out"):new Error(`AJAX request failed: ${i}`))}});else{const e=new AbortController,s=setTimeout((()=>e.abort()),i);fetch(window.ajaxurl,{method:"POST",body:n,signal:e.signal}).then((e=>{if(clearTimeout(s),!e.ok)throw new Error(`Server returned ${e.status}: ${e.statusText}`);return e.json()})).then((e=>{e.success?t(e.data):a(new Error(e.data?.message||"Unknown error occurred"))})).catch((e=>{clearTimeout(s),"AbortError"===e.name?a(new Error("Request timed out")):a(e)}))}}))})(n,{revalidate:e});s?.data&&i(s.data),d(!1)};return(0,v.jsxs)("div",{className:"page review-page",children:[(0,v.jsxs)("div",{className:"review-fetch",children:[(0,v.jsx)(j.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,g.__)("Trustpilot URL","trustpilot-reviewkit"),type:"url",help:(0,g.__)("Enter the URL of your Trustpilot page","trustpilot-reviewkit"),value:n,onChange:e=>l(e)}),(0,v.jsxs)("div",{className:"button-container",children:[(0,v.jsx)(j.Button,{variant:"primary",onClick:m,disabled:""===n,children:(0,g.__)("Fetch Reviews","trustpilot-reviewkit")}),(0,v.jsx)(j.Button,{variant:"secondary",onClick:()=>m(!0),disabled:!s,children:(0,g.__)("Revalidate Reviews","trustpilot-reviewkit")})]})]}),c?(0,v.jsx)("div",{style:{marginTop:"20px",textAlign:"center"},children:(0,v.jsx)(j.Spinner,{style:{height:"calc(4px * 10)",width:"calc(4px * 10)"}})}):(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(D,{businessDetails:t}),(0,v.jsx)($,{reviews:a})]})]})},A=()=>{const{tab:e}=w();return(0,v.jsxs)("div",{children:[(0,v.jsx)(x,{}),"reviews"===e&&(0,v.jsx)(B,{}),"shortcodes"===e&&(0,v.jsx)(L,{})]})};document.addEventListener("DOMContentLoaded",(function(){const e=document.getElementById("trustpilot-reviewskit-body");(0,s.createRoot)(e).render((0,v.jsx)(p,{children:(0,v.jsx)(A,{})}))}))})();