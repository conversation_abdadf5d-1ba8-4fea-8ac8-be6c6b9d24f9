import React, { useState } from "react";
import ReviewCard from "./ReviewCard";

/**
 * ReviewList component for displaying a list of reviews
 * @param {Object} reviews - Reviews object from API response (keyed by review ID)
 * @param {number} initialDisplayCount - Initial number of reviews to display (default: 10)
 * @param {boolean} showLoadMore - Whether to show load more button (default: true)
 */
const ReviewList = ({ 
  reviews = {}, 
  initialDisplayCount = 10, 
  showLoadMore = true 
}) => {
  const [displayCount, setDisplayCount] = useState(initialDisplayCount);

  // Convert reviews object to array and sort by published date (newest first)
  const reviewsArray = Object.values(reviews).sort((a, b) => {
    const dateA = new Date(a.dates?.publishedDate || 0);
    const dateB = new Date(b.dates?.publishedDate || 0);
    return dateB - dateA;
  });

  const totalReviews = reviewsArray.length;
  const displayedReviews = reviewsArray.slice(0, displayCount);
  const hasMoreReviews = displayCount < totalReviews;

  // Load more reviews
  const handleLoadMore = () => {
    setDisplayCount(prev => Math.min(prev + initialDisplayCount, totalReviews));
  };

  // Reset to initial count
  const handleShowLess = () => {
    setDisplayCount(initialDisplayCount);
  };

  // Filter reviews by rating
  const [ratingFilter, setRatingFilter] = useState(null);
  
  const filteredReviews = ratingFilter 
    ? displayedReviews.filter(review => review.rating === ratingFilter)
    : displayedReviews;

  // Get rating distribution
  const getRatingDistribution = () => {
    const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    reviewsArray.forEach(review => {
      if (review.rating >= 1 && review.rating <= 5) {
        distribution[Math.floor(review.rating)]++;
      }
    });
    return distribution;
  };

  const ratingDistribution = getRatingDistribution();

  if (totalReviews === 0) {
    return (
      <div className="review-list">
        <div className="review-list-empty">
          <p>No reviews available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="review-list">
      <div className="review-list-header">
        <h3 className="review-list-title">
          Customer Reviews ({totalReviews})
        </h3>
        
        {/* Rating Filter */}
        <div className="review-filters">
          <div className="rating-filter">
            <button
              className={`filter-btn ${ratingFilter === null ? 'active' : ''}`}
              onClick={() => setRatingFilter(null)}
            >
              All
            </button>
            {[5, 4, 3, 2, 1].map(rating => (
              <button
                key={rating}
                className={`filter-btn ${ratingFilter === rating ? 'active' : ''}`}
                onClick={() => setRatingFilter(rating)}
              >
                {rating}★ ({ratingDistribution[rating]})
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="review-list-content">
        {filteredReviews.length === 0 ? (
          <div className="review-list-empty">
            <p>No reviews found for the selected rating</p>
          </div>
        ) : (
          <div className="review-cards">
            {filteredReviews.map((review) => (
              <ReviewCard 
                key={review.reviewId} 
                review={review} 
              />
            ))}
          </div>
        )}
      </div>

      {/* Load More / Show Less Controls */}
      {showLoadMore && ratingFilter === null && (
        <div className="review-list-controls">
          {hasMoreReviews && (
            <button 
              className="load-more-btn"
              onClick={handleLoadMore}
            >
              Load More Reviews ({totalReviews - displayCount} remaining)
            </button>
          )}
          
          {displayCount > initialDisplayCount && (
            <button 
              className="show-less-btn"
              onClick={handleShowLess}
            >
              Show Less
            </button>
          )}
        </div>
      )}

      {/* Review Summary */}
      <div className="review-summary">
        <p className="review-count-summary">
          Showing {filteredReviews.length} of {totalReviews} reviews
        </p>
      </div>
    </div>
  );
};

export default ReviewList;
