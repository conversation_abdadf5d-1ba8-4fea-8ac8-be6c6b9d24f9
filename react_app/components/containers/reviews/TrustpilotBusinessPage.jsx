import React from "react";
import StarRating from "./StarRating";

/**
 * TrustpilotBusinessPage component for displaying business information
 * @param {Object} businessDetails - Business details object from API response
 */
const TrustpilotBusinessPage = ({ businessDetails = {} }) => {
  const {
    displayName = "",
    identifyingName = "",
    numberOfReviews = 0,
    trustScore = 0,
    websiteUrl = "",
    websiteTitle = "",
    profileImageUrl = "",
    stars = 0,
    categories = [],
    activity = {}
  } = businessDetails;

  // Format large numbers
  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M";
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K";
    }
    return num.toString();
  };

  // Get primary category
  const primaryCategory = categories.find(cat => cat.isPrimary)?.name || "";

  // Check if business is verified
  const isVerified = activity?.verification?.verifiedUserIdentity || false;
  const isClaimed = activity?.isClaimed || false;

  // Format profile image URL
  const getImageUrl = (url) => {
    if (!url) return "";
    // Add https: if URL starts with //
    if (url.startsWith("//")) {
      return `https:${url}`;
    }
    return url;
  };

  if (!displayName) {
    return (
      <div className="trustpilot-business-page">
        <div className="business-placeholder">
          <p>No business information available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="trustpilot-business-page">
      <div className="business-header">
        <div className="business-logo">
          {profileImageUrl ? (
            <img
              src={getImageUrl(profileImageUrl)}
              alt={`${displayName} logo`}
              className="business-image"
              onError={(e) => {
                e.target.style.display = "none";
                e.target.nextSibling.style.display = "flex";
              }}
            />
          ) : null}
          <div
            className="business-initials"
            style={{ display: profileImageUrl ? "none" : "flex" }}
          >
            {displayName.charAt(0).toUpperCase()}
          </div>
        </div>

        <div className="business-info">
          <div className="business-name-section">
            <h2 className="business-name">{displayName}</h2>
            {isClaimed && (
              <span className="business-claimed">✓ Claimed</span>
            )}
            {isVerified && (
              <span className="business-verified">✓ Verified</span>
            )}
          </div>

          {identifyingName && (
            <p className="business-website">{identifyingName}</p>
          )}

          {primaryCategory && (
            <p className="business-category">{primaryCategory}</p>
          )}

          <div className="business-rating">
            <StarRating
              rating={trustScore}
              size="large"
              showRating={true}
              useSvg={true}
            />
            <span className="review-count">
              Based on {formatNumber(numberOfReviews)} reviews
            </span>
          </div>

          {websiteUrl && (
            <div className="business-links">
              <a
                href={websiteUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="business-website-link"
              >
                Visit Website
              </a>
            </div>
          )}
        </div>
      </div>

      {activity?.replyBehavior && (
        <div className="business-stats">
          <div className="stat-item">
            <span className="stat-label">Reply Rate:</span>
            <span className="stat-value">
              {activity.replyBehavior.replyPercentage}%
            </span>
          </div>
          {activity.replyBehavior.averageDaysToReply && (
            <div className="stat-item">
              <span className="stat-label">Avg. Reply Time:</span>
              <span className="stat-value">
                {Math.round(activity.replyBehavior.averageDaysToReply)} days
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TrustpilotBusinessPage;
