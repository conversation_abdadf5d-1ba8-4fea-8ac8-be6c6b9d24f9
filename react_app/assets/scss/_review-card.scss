// Review Card Component Styles
.review-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  transition: box-shadow 0.2s ease, transform 0.2s ease;

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }

  .review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;

    .review-meta {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;

      .review-date {
        font-size: 12px;
        color: #666;
        white-space: nowrap;
      }
    }
  }

  .review-content {
    .review-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
      line-height: 1.4;
    }

    .review-body {
      font-size: 14px;
      line-height: 1.6;
      color: #555;
      margin: 0 0 12px 0;
      word-wrap: break-word;
    }

    .review-experience-date {
      margin-top: 8px;

      small {
        color: #888;
        font-size: 12px;
      }
    }
  }

  .review-footer {
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;

    .review-link {
      color: #007bff;
      text-decoration: none;
      font-size: 13px;
      font-weight: 500;
      transition: color 0.2s ease;

      &:hover {
        color: #0056b3;
        text-decoration: underline;
      }

      &::after {
        content: " ↗";
        font-size: 11px;
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 16px;
    margin-bottom: 12px;

    .review-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;

      .review-meta {
        align-items: flex-start;
        flex-direction: row;
        gap: 12px;
      }
    }

    .review-content {
      .review-title {
        font-size: 15px;
      }

      .review-body {
        font-size: 13px;
      }
    }
  }
}
